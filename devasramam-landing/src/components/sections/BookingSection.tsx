"use client";

import React, { useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, Star, Shield, Plus, Minus, MessageCircle, CalendarDays, Loader2, RefreshCw } from "lucide-react";
import { BRAND, PRICING } from "@/lib/constants";
import { motion } from "framer-motion";
import WhatsAppIcon from "@/components/ui/WhatsAppIcon";
import { DateRange } from "react-day-picker";
import { useAvailability } from "@/hooks/useAvailability";

export default function BookingSection() {
  const [selectedDates, setSelectedDates] = useState<DateRange | undefined>(undefined);
  const [guests, setGuests] = useState(2);
  const { blockedDates, isLoading: availabilityLoading, error: availabilityError, refetch } = useAvailability();

  // Helper function to check if a date is blocked
  const isDateBlocked = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return blockedDates.some(blockedDate =>
      blockedDate.toISOString().split('T')[0] === dateStr
    );
  };

  // Helper function to check if date range contains any blocked dates
  const isRangeBlocked = (from: Date, to: Date) => {
    const current = new Date(from);
    while (current <= to) {
      if (isDateBlocked(current)) {
        return true;
      }
      current.setDate(current.getDate() + 1);
    }
    return false;
  };

  // Calculate pricing dynamically
  const pricePerNight = PRICING.calculatePrice(guests);
  const numberOfNights = selectedDates?.from && selectedDates?.to
    ? Math.ceil((selectedDates.to.getTime() - selectedDates.from.getTime()) / (1000 * 60 * 60 * 24))
    : 0;
  const totalPrice = numberOfNights * pricePerNight;

  const handleBooking = () => {
    if (selectedDates?.from && selectedDates?.to) {
      // Check if the selected range contains any blocked dates
      if (isRangeBlocked(selectedDates.from, selectedDates.to)) {
        alert('The selected date range contains unavailable dates. Please choose different dates.');
        return;
      }

      const checkIn = selectedDates.from.toDateString();
      const checkOut = selectedDates.to.toDateString();
      const message = `Hi! I'd like to book Devasramam for ${guests} guest(s) from ${checkIn} to ${checkOut} (${numberOfNights} nights). Total: ₹${totalPrice.toLocaleString()}. Looking forward to experiencing this architectural masterpiece!`;
      window.open(`https://wa.me/${BRAND.whatsapp}?text=${encodeURIComponent(message)}`, '_blank');
    }
  };

  const handleQuickInquiry = () => {
    const message = "Hi! I'm interested in staying at Devasramam. Could you please share more details about availability and pricing?";
    window.open(`https://wa.me/${BRAND.whatsapp}?text=${encodeURIComponent(message)}`, '_blank');
  };

  return (
    <section id="booking" className="relative py-24 px-4 bg-gradient-to-b from-white to-gray-50 overflow-hidden">
      {/* Aesthetic Background Shapes */}
      <div className="absolute top-16 left-10 w-28 h-28 bg-[#004D40]/8 rounded-full blur-2xl"></div>
      <div className="absolute top-1/3 right-12 w-20 h-20 bg-[#8BC34A]/12 transform rotate-45 blur-xl"></div>
      <div className="absolute bottom-24 left-1/4 w-24 h-24 bg-[#004D40]/6 transform rotate-12 blur-lg"></div>
      <div className="absolute bottom-16 right-16 w-32 h-32 bg-[#8BC34A]/10 rounded-full blur-2xl"></div>
      <div className="absolute top-1/2 left-8 w-16 h-16 bg-[#004D40]/10 transform -rotate-12 blur-md"></div>

      <div className="max-w-6xl mx-auto relative">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="bg-[#004D40] text-white px-4 py-2 rounded-full mb-4">
            Limited Availability
          </Badge>

          <h2 className="text-4xl lg:text-5xl font-bold text-[#333333] mb-4"
              style={{ fontFamily: 'var(--font-lora)' }}>
            Book Your Stay
          </h2>
          <p className="text-xl text-[#666666] max-w-2xl mx-auto">
            Secure your dates at this architectural masterpiece. Limited bookings ensure an intimate experience.
          </p>
        </div>

        {/* Clean 2-column booking layout */}
        <div className="max-w-6xl mx-auto">
          <Card className="p-6 lg:p-8 shadow-xl border-none">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
              {/* Left Column - Calendar Section */}
              <div className="space-y-6">
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-bold text-[#333333]" style={{ fontFamily: 'var(--font-lora)' }}>
                      Choose Your Dates
                    </h3>
                    {availabilityLoading && (
                      <div className="flex items-center text-sm text-[#666666]">
                        <Loader2 size={16} className="animate-spin mr-1" />
                        Loading availability...
                      </div>
                    )}
                    {availabilityError && (
                      <button
                        onClick={refetch}
                        className="flex items-center text-sm text-[#004D40] hover:text-[#00695C] transition-colors"
                        title="Retry loading availability"
                      >
                        <RefreshCw size={16} className="mr-1" />
                        Retry
                      </button>
                    )}
                  </div>
                  <p className="text-[#666666]">
                    Select check-in and check-out dates
                    {!availabilityLoading && !availabilityError && blockedDates.length > 0 && (
                      <span className="text-[#004D40] ml-2">• Real-time availability from Airbnb ({blockedDates.length} dates blocked)</span>
                    )}
                    {!availabilityLoading && !availabilityError && blockedDates.length === 0 && (
                      <span className="text-green-600 ml-2">• All dates currently available!</span>
                    )}
                  </p>
                  {availabilityError && (
                    <p className="text-sm text-red-600 mt-1">
                      Unable to load real-time availability. Some dates may not be accurate.
                    </p>
                  )}
                </div>

                <div className="bg-white rounded-xl p-2 sm:p-4 border border-gray-200 overflow-hidden flex justify-center">
                  <Calendar
                    mode="range"
                    selected={selectedDates}
                    onSelect={setSelectedDates}
                    disabled={(date) => {
                      // Disable past dates
                      if (date < new Date()) return true;
                      // Disable blocked dates from Airbnb
                      return isDateBlocked(date);
                    }}
                    className="rounded-md border-none"
                    numberOfMonths={1}
                    classNames={{
                      months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                      month: "space-y-4",
                      caption: "flex justify-between items-center pt-1 relative px-2",
                      caption_label: "text-sm font-medium",
                      nav: "flex items-center justify-between w-full",
                      nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-gray-100 rounded transition-all duration-200 flex items-center justify-center",
                      nav_button_previous: "order-first",
                      nav_button_next: "order-last",
                      table: "w-full border-collapse space-y-1",
                      head_row: "flex",
                      head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                      row: "flex w-full mt-2",
                      cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                      day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100 text-xs sm:text-sm",
                      day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                      day_today: "bg-accent text-accent-foreground",
                      day_outside: "text-muted-foreground opacity-50",
                      day_disabled: "text-muted-foreground opacity-50",
                      day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                      day_hidden: "invisible",
                    }}
                  />
                </div>

                {/* Calendar Legend */}
                <div className="bg-gray-50 rounded-lg p-3 text-xs">
                  <div className="flex flex-wrap gap-4 justify-center">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-[#004D40] rounded mr-2"></div>
                      <span className="text-[#666666]">Selected</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-gray-300 rounded mr-2"></div>
                      <span className="text-[#666666]">Unavailable</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-white border border-gray-300 rounded mr-2"></div>
                      <span className="text-[#666666]">Available</span>
                    </div>
                  </div>
                </div>

                {selectedDates?.from && (
                  <div className="mt-4 text-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedDates(undefined)}
                      className="border-[#004D40] text-[#004D40] hover:bg-[#004D40] hover:text-white transition-colors"
                    >
                      Clear selection
                    </Button>
                  </div>
                )}
              </div>

              {/* Right Column - Guest Selection & Booking Summary */}
              <div className="space-y-6">
                {/* Guest Selection - Always visible */}
                <div className="bg-white rounded-xl p-6 border border-gray-200">
                  <h4 className="font-bold text-[#333333] mb-4" style={{ fontFamily: 'var(--font-lora)' }}>
                    <Users size={20} className="inline mr-2" />
                    Number of Guests
                  </h4>
                  <div className="flex items-center justify-between">
                    <span className="text-[#666666]">Guests (max 2):</span>
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => setGuests(Math.max(1, guests - 1))}
                        className="w-10 h-10 rounded-full border-2 border-[#004D40] text-[#004D40] flex items-center justify-center hover:bg-[#004D40] hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={guests <= 1}
                      >
                        <Minus size={18} />
                      </button>
                      <span className="font-bold text-xl w-12 text-center">{guests}</span>
                      <button
                        onClick={() => setGuests(Math.min(2, guests + 1))}
                        className="w-10 h-10 rounded-full border-2 border-[#004D40] text-[#004D40] flex items-center justify-center hover:bg-[#004D40] hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={guests >= 2}
                      >
                        <Plus size={18} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Booking Summary or Helpful Tips */}
                {selectedDates?.from ? (
                  <div className="bg-[#004D40]/5 rounded-xl p-6 border border-[#004D40]/10">
                    <h4 className="font-bold text-[#333333] mb-4" style={{ fontFamily: 'var(--font-lora)' }}>
                      Booking Summary
                    </h4>

                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between">
                        <span className="text-[#666666]">Check-in:</span>
                        <span className="font-medium">{selectedDates.from.toLocaleDateString()}</span>
                      </div>
                      {selectedDates.to && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-[#666666]">Check-out:</span>
                            <span className="font-medium">{selectedDates.to.toLocaleDateString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-[#666666]">Nights:</span>
                            <span className="font-medium">{numberOfNights}</span>
                          </div>
                        </>
                      )}

                      <div className="flex justify-between">
                        <span className="text-[#666666]">Guests:</span>
                        <span className="font-medium">{guests}</span>
                      </div>

                      {selectedDates.to && (
                        <div className="border-t border-[#004D40]/20 pt-3">
                          <div className="flex justify-between items-center">
                            <span className="text-[#666666]">Total:</span>
                            <span className="text-xl font-bold text-[#004D40]">
                              ₹{totalPrice.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>

                    {selectedDates.to ? (
                      selectedDates.from && selectedDates.to && isRangeBlocked(selectedDates.from, selectedDates.to) ? (
                        <div className="text-center">
                          <div className="text-red-600 text-sm mb-2">
                            ⚠️ Selected dates contain unavailable periods
                          </div>
                          <Button
                            variant="outline"
                            className="w-full border-red-300 text-red-600 hover:bg-red-50 rounded-xl py-3"
                            disabled
                          >
                            Dates Unavailable
                          </Button>
                        </div>
                      ) : (
                        <Button
                          className="w-full bg-[#004D40] hover:bg-[#00695C] text-white rounded-xl py-3"
                          onClick={handleBooking}
                        >
                          <WhatsAppIcon size={18} className="mr-2" />
                          Book Now on WhatsApp
                        </Button>
                      )
                    ) : (
                      <div className="text-center text-[#666666] text-sm">
                        Please select your check-out date to continue
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="bg-gradient-to-br from-[#004D40]/5 to-[#004D40]/10 rounded-xl p-6 text-center border border-[#004D40]/20">
                    <CalendarDays size={48} className="mx-auto text-[#004D40] mb-4" />
                    <h4 className="font-bold text-[#333333] mb-2" style={{ fontFamily: 'var(--font-lora)' }}>
                      Please Select Your Dates
                    </h4>
                    <p className="text-sm text-[#666666] mb-4">
                      Choose your check-in and check-out dates from the calendar to see pricing and availability
                    </p>
                    <div className="text-sm text-[#666666] space-y-1">
                      <div className="font-medium text-[#004D40]">₹{PRICING.calculatePrice(guests).toLocaleString()}/night</div>
                      <div>for {guests} guest{guests > 1 ? 's' : ''}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Quick Contact Section */}
        <motion.div
          className="max-w-3xl mx-auto mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="p-6 shadow-lg border-none bg-gradient-to-r from-[#004D40]/5 to-[#00695C]/5">
            <div className="text-center">
              <h3 className="text-xl font-bold text-[#333333] mb-2" style={{ fontFamily: 'var(--font-lora)' }}>
                Need Help with Your Booking?
              </h3>
              <p className="text-[#666666] mb-4">
                Get instant assistance from our host via WhatsApp
              </p>
              <Button
                variant="outline"
                className="border-[#004D40] text-[#004D40] hover:bg-[#004D40] hover:text-white"
                onClick={handleQuickInquiry}
              >
                <WhatsAppIcon size={16} className="mr-2" />
                Quick Inquiry on WhatsApp
              </Button>
            </div>
          </Card>
        </motion.div>

        {/* Trust Signals */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
          <div className="flex flex-col items-center space-y-3">
            <div className="w-16 h-16 bg-[#004D40]/10 rounded-full flex items-center justify-center">
              <Shield size={24} className="text-[#004D40]" />
            </div>
            <h4 className="font-semibold text-[#333333]">Secure Booking</h4>
            <p className="text-sm text-[#666666]">Your reservation is protected with our secure booking system</p>
          </div>

          <div className="flex flex-col items-center space-y-3">
            <div className="w-20 h-20 bg-[#004D40]/10 rounded-full flex items-center justify-center">
              <Star size={36} className="text-[#004D40] fill-[#004D40]" />
            </div>
            <p className="text-sm text-[#666666]">Consistently rated 5 stars by our guests</p>
          </div>

          <div className="flex flex-col items-center space-y-3">
            <div className="w-16 h-16 bg-[#004D40]/10 rounded-full flex items-center justify-center">
              <MessageCircle size={24} className="text-[#004D40]" />
            </div>
            <h4 className="font-semibold text-[#333333]">24/7 Support</h4>
            <p className="text-sm text-[#666666]">Get instant help via WhatsApp anytime</p>
          </div>
        </div>
      </div>
    </section>
  );
}