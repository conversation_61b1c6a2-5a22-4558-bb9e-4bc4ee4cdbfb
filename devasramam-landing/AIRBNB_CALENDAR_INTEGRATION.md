# Airbnb Calendar Integration

This document explains the Airbnb iCal calendar integration implemented for the Devasramam booking system.

## Overview

The integration fetches real-time availability data from Airbnb's iCal feed and displays blocked dates in the booking calendar, preventing double bookings and providing accurate availability information to potential guests.

## Implementation Details

### 1. API Route (`/api/availability`)
- **File**: `src/app/api/availability/route.ts`
- **Purpose**: Fetches and parses Airbnb's iCal data
- **Caching**: 1-hour cache to avoid hitting Airbnb's servers too frequently
- **Returns**: JSON with blocked dates and last updated timestamp

### 2. Custom Hook (`useAvailability`)
- **File**: `src/hooks/useAvailability.ts`
- **Purpose**: Manages availability data state in React components
- **Features**: Loading states, error handling, refetch capability

### 3. Calendar Integration
- **File**: `src/components/sections/BookingSection.tsx`
- **Features**:
  - Disables blocked dates in the calendar
  - Validates selected date ranges
  - Shows loading and error states
  - Prevents booking of unavailable dates

## Configuration

The Airbnb iCal URL is configured in `src/lib/constants.ts`:

```typescript
export const BRAND = {
  // ... other properties
  airbnbIcalUrl: "https://www.airbnb.com/calendar/ical/573653202420763928.ics?s=b765435b7981835c4f4b723dbdc89d84&locale=en-IN",
}
```

## Features

### Real-time Availability
- Fetches blocked dates from Airbnb every hour
- Displays unavailable dates as disabled in the calendar
- Shows real-time status indicators

### User Experience
- Loading spinner while fetching availability
- Error handling with retry option
- Visual indicators for blocked vs available dates
- Prevents booking of unavailable date ranges

### Validation
- Checks if selected date range contains blocked dates
- Shows warning if unavailable dates are selected
- Disables booking button for invalid selections

## Testing

To test the integration:

1. Start the development server: `npm run dev`
2. Navigate to the booking section
3. Check that blocked dates are disabled in the calendar
4. Try selecting a range that includes blocked dates
5. Verify the API endpoint: `curl http://localhost:3002/api/availability`

## Maintenance

### Updating the iCal URL
If the Airbnb iCal URL changes, update the `airbnbIcalUrl` in `src/lib/constants.ts`.

### Cache Duration
The API caches responses for 1 hour. To change this, modify the `revalidate` value in the API route.

### Error Handling
The system gracefully handles:
- Network failures
- Invalid iCal data
- Airbnb server downtime
- Malformed date ranges

## Dependencies

- `node-ical`: For parsing iCal data (alternative to ical.js)
- `react-day-picker`: Calendar component
- Next.js API routes for server-side fetching

## Security Considerations

- Uses server-side fetching to avoid CORS issues
- Implements rate limiting through caching
- Validates all date inputs
- Handles errors gracefully without exposing sensitive information
